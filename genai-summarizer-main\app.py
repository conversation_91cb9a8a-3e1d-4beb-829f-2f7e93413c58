import streamlit as st
import time
from typing import Dict, List, Tuple, Optional, Any
import os
import json
import tempfile
from pathlib import Path

# Import document processing and RAG
from document_store import DocumentStore
from rag_qa import RAGQA
from summarizer import generate_summary
from challenge_mode import generate_questions, evaluate_answer

# Initialize document store and RAG QA
@st.cache_resource
def get_document_store():
    return DocumentStore(persist_dir="./vector_store")

@st.cache_resource
def get_rag_qa():
    return RAGQA(get_document_store())

# Initialize session state
if 'document_store' not in st.session_state:
    st.session_state.document_store = get_document_store()
    st.session_state.rag_qa = get_rag_qa()
    st.session_state.uploaded_docs = {}
    st.session_state.current_doc_id = None
    st.session_state.document_qa = None

# File upload and document management
def handle_file_upload(uploaded_file):
    """Process uploaded file and add to document store"""
    try:
        # Save uploaded file to temp location
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_path = tmp_file.name
        
        # Add to document store
        doc_id = st.session_state.document_store.add_document(
            tmp_path,
            metadata={
                'filename': uploaded_file.name,
                'upload_time': time.strftime("%Y-%m-%d %H:%M:%S")
            }
        )
        
        # Update session state
        st.session_state.uploaded_docs[doc_id] = {
            'filename': uploaded_file.name,
            'upload_time': time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Set as current document if first upload
        if st.session_state.current_doc_id is None:
            st.session_state.current_doc_id = doc_id
            st.session_state.document_qa = st.session_state.rag_qa.get_document_qa(doc_id)
        
        # Clean up temp file
        os.unlink(tmp_path)
        
        return True, "Document uploaded successfully!"
    except Exception as e:
        return False, f"Error processing file: {str(e)}"

# Set page config
st.set_page_config(
    page_title="GenAI Document Assistant",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Sidebar for document management
with st.sidebar:
    st.title("📂 Document Management")
    
    # File uploader
    uploaded_file = st.file_uploader(
        "Upload a document (PDF, DOCX, TXT, XLSX)",
        type=['pdf', 'docx', 'txt', 'xlsx', 'xls']
    )
    
    if uploaded_file is not None:
        with st.spinner("Processing document..."):
            success, message = handle_file_upload(uploaded_file)
            if success:
                st.success(message)
            else:
                st.error(message)
    
    # Document selector
    if st.session_state.uploaded_docs:
        st.subheader("Your Documents")
        for doc_id, doc_info in st.session_state.uploaded_docs.items():
            col1, col2 = st.columns([1, 0.2])
            with col1:
                if st.button(
                    f"📄 {doc_info['filename']}",
                    key=f"doc_{doc_id}",
                    use_container_width=True,
                    type="primary" if doc_id == st.session_state.current_doc_id else "secondary"
                ):
                    st.session_state.current_doc_id = doc_id
                    st.session_state.document_qa = st.session_state.rag_qa.get_document_qa(doc_id)
                    st.rerun()
            with col2:
                if st.button("🗑️", key=f"del_{doc_id}"):
                    if st.session_state.document_store.delete_document(doc_id):
                        del st.session_state.uploaded_docs[doc_id]
                        if st.session_state.current_doc_id == doc_id:
                            st.session_state.current_doc_id = next(iter(st.session_state.uploaded_docs), None)
                            if st.session_state.current_doc_id:
                                st.session_state.document_qa = st.session_state.rag_qa.get_document_qa(st.session_state.current_doc_id)
                            else:
                                st.session_state.document_qa = None
                        st.rerun()

# Main content area
st.title("📄 GenAI Document Assistant")

# Custom CSS for better UI
st.markdown("""
<style>
    .stButton>button {
        transition: all 0.3s ease;
    }
    .stButton>button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .stTextArea>div>div>textarea {
        min-height: 150px;
    }
    .stProgress > div > div > div > div {
        background-color: #4CAF50;
    }
    .stAlert {
        border-radius: 10px;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'document_text' not in st.session_state:
    st.session_state.document_text = ""

if 'summary' not in st.session_state:
    st.session_state.summary = ""

if 'questions' not in st.session_state:
    st.session_state.questions = []

if 'show_questions' not in st.session_state:
    st.session_state.show_questions = False

if 'show_results' not in st.session_state:
    st.session_state.show_results = False

# Initialize chat and app state
if "messages" not in st.session_state:
    st.session_state.messages = []

if "question_history" not in st.session_state:
    st.session_state.question_history = []

# Initialize challenge mode state
if "challenge_mode" not in st.session_state:
    st.session_state.challenge_mode = False
    st.session_state.questions = []
    st.session_state.current_question_index = 0
    st.session_state.user_answers = {}
    st.session_state.show_questions = False
    st.session_state.quiz_completed = False

# Document content area
if st.session_state.current_doc_id:
    doc_info = st.session_state.uploaded_docs[st.session_state.current_doc_id]
    st.header(doc_info['filename'])
    
    # Document summary tab
    tab1, tab2 = st.tabs(["📝 Summary", "📊 Details"])
    
    with tab1:
        with st.spinner("Generating document summary..."):
            summary = st.session_state.document_qa.get_document_summary()
            st.markdown(summary)
    
    with tab2:
        st.json(doc_info)
        
        # Show document chunks (for debugging)
        if st.checkbox("Show document chunks"):
            chunks = st.session_state.document_store.get_document_chunks(st.session_state.current_doc_id)
            for i, chunk in enumerate(chunks):
                with st.expander(f"Chunk {i + 1}"):
                    st.text(chunk['text'][:500] + "..." if len(chunk['text']) > 500 else chunk['text'])
    
    st.divider()
else:
    st.info("👈 Upload a document to get started")

# Chat interface
st.subheader("💬 Chat with your document")

# Display chat messages from history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Accept user input
if prompt := st.chat_input("Ask a question about the document..."):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    
    # Display user message in chat message container
    with st.chat_message("user"):
        st.markdown(prompt)
    
    # Display assistant response in chat message container
    with st.chat_message("assistant"):
        message_placeholder = st.empty()
        full_response = ""
        
        # Check if we have a document to query
        if not st.session_state.current_doc_id:
            full_response = "Please upload and select a document first."
        else:
            try:
                # Show thinking indicator
                with st.spinner("Analyzing document..."):
                    # Get response from RAG QA
                    response = st.session_state.document_qa.answer_question(prompt)
                    
                    # Format the response
                    if response.get("answer"):
                        full_response = response["answer"]
                        
                        # Add sources if available
                        if response.get("sources"):
                            full_response += "\n\n**Sources:**\n"
                            for i, source in enumerate(response["sources"][:2], 1):
                                full_response += f"{i}. {source['text'][:200]}...\n"
                        
                        # Add to question history
                        st.session_state.question_history.append({
                            "question": prompt,
                            "answer": response["answer"],
                            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                        })
                    else:
                        full_response = "I couldn't find a clear answer in the document."
            except Exception as e:
                full_response = f"An error occurred: {str(e)}"
        
        # Display the response with typing effect
        message_placeholder.markdown(full_response)
    
    # Add assistant response to chat history
    st.session_state.messages.append({"role": "assistant", "content": full_response})

# Challenge Mode
st.markdown("---")
st.subheader("🎯 Challenge Me: Test Your Understanding")

if not st.session_state.current_doc_id:
    st.info("ℹ️ Please upload and select a document first to use Challenge Mode.")
else:
    # Generate Questions Button
    if st.button("🎯 Generate Challenge Questions", use_container_width=True):
        with st.spinner("🤔 Creating challenging questions..."):
            try:
                # Get document text from chunks
                doc_chunks = st.session_state.document_store.get_document_chunks(st.session_state.current_doc_id)
                document_text = "\n\n".join([chunk['text'] for chunk in doc_chunks])
                
                st.session_state.questions = generate_questions(document_text)
                st.session_state.show_questions = True
                st.session_state.show_results = False
                st.session_state.user_answers = {}
                st.session_state.quiz_completed = False
                st.success("🧠 Challenge questions generated!")
            except Exception as e:
                st.error(f"Failed to generate questions: {str(e)}")
    
    # Display questions if available
    if st.session_state.show_questions and st.session_state.questions:
        st.markdown("### 📝 Answer the following questions:")
        
        # Ensure questions is a list of dictionaries
        if isinstance(st.session_state.questions[0], str):
            st.session_state.questions = [{"question": q} for q in st.session_state.questions]
        
        for i, q in enumerate(st.session_state.questions):
            question_text = q.get('question', 'No question text')
            st.text_area(f"Question {i+1}", value=question_text, disabled=True, key=f"q_{i}")
            
            # Initialize answer in session state if not exists
            if f"answer_{i}" not in st.session_state:
                st.session_state[f"answer_{i}"] = ""
            
            # Get user's answer
            answer = st.text_area(
                f"Your answer {i+1}",
                key=f"user_answer_{i}",
                value=st.session_state.get(f"answer_{i}", ""),
                placeholder="Type your answer here..."
            )
            st.session_state[f"answer_{i}"] = answer
        
        # Submit button
        if st.button("✅ Submit Answers", use_container_width=True):
            st.session_state.show_results = True
            st.session_state.quiz_completed = True
            
            # Evaluate answers
            st.session_state.scores = {}
            for i, q in enumerate(st.session_state.questions):
                user_answer = st.session_state.get(f"answer_{i}", "")
                if not user_answer.strip():
                    st.warning(f"Please provide an answer for question {i+1}")
                    st.session_state.show_results = False
                    break
                
                # Get document context for evaluation
                doc_chunks = st.session_state.document_store.get_document_chunks(st.session_state.current_doc_id)
                document_text = "\n\n".join([chunk['text'] for chunk in doc_chunks])
                
                score = evaluate_answer(
                    question=q.get('question', ''),
                    user_answer=user_answer,
                    document_text=document_text
                )
                st.session_state.scores[i] = score
        
        # Show results if available
        if st.session_state.show_results and st.session_state.quiz_completed:
            st.markdown("### 📊 Your Results")
            total_score = 0
            
            for i, q in enumerate(st.session_state.questions):
                score = st.session_state.scores.get(i, 0)
                total_score += score
                
                st.markdown(f"**Question {i+1}:** {q.get('question', '')}")
                st.markdown(f"**Your Answer:** {st.session_state.get(f'answer_{i}', '')}")
                st.markdown(f"**Score:** {score}/100")
                st.progress(score/100)
                st.write("---")
            
            # Calculate and display overall score
            avg_score = total_score / len(st.session_state.questions) if st.session_state.questions else 0
            st.success(f"### 🎉 Overall Score: {avg_score:.1f}/100")
            
            # Provide feedback based on score
            if avg_score >= 80:
                st.balloons()
                st.success("Excellent work! You have a strong understanding of the document.")
            elif avg_score >= 60:
                st.success("Good job! You have a good understanding, but there's room for improvement.")
            elif avg_score >= 40:
                st.warning("Not bad! Consider reviewing the document again.")
            else:
                st.error("You might want to go through the document again. Keep trying!")
            
            # Option to retry
            if st.button("🔄 Try Again", use_container_width=True):
                st.session_state.show_results = False
                st.session_state.quiz_completed = False
                for i in range(len(st.session_state.questions)):
                    st.session_state[f"answer_{i}"] = ""
                st.rerun()
