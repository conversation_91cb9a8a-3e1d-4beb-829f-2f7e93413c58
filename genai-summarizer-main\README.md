


# 🧠 GenAI Smart Research Summarizer

An AI-powered assistant to read, understand, summarize, and quiz you on uploaded documents (PDF/TXT). Built for the EZ Works GenAI Internship Assignment.

---

## 🔧 Features

- 📄 **Document Upload** (PDF or TXT)
- ✍️ **Auto Summary** in ≤150 words
- 💬 **Ask Anything Mode** — free-form Q&A
- 🎯 **Challenge Me Mode** — generates logic-based questions and evaluates your answers
- 🧠 Runs locally using Hugging Face models (no API keys required)

---

## 🛠️ Tech Stack

- Python 🐍
- Streamlit 🖼️
- HuggingFace Transformers 🤗
- pdfminer.six (PDF parsing)

---

## 🚀 Getting Started

### 1. Clone the repo
```bash
git clone https://github.com/your-username/genai-summarizer.git
cd genai-summarizer
# 🧠 GenAI Smart Research Summarizer

An AI-powered assistant to read, understand, summarize, and quiz you on uploaded documents (PDF/TXT). Built for the EZ Works GenAI Internship Assignment.

---

## 🔧 Features

- 📄 **Document Upload** (PDF or TXT)
- ✍️ **Auto Summary** in ≤150 words
- 💬 **Ask Anything Mode** — free-form Q&A
- 🎯 **Challenge Me Mode** — generates logic-based questions and evaluates your answers
- 🧠 Runs locally using Hugging Face models (no API keys required)

---

## 🛠️ Tech Stack

- Python 🐍
- Streamlit 🖼️
- HuggingFace Transformers 🤗
- pdfminer.six (PDF parsing)

---

## 🚀 Getting Started

### 1. Clone the repo
```bash
git clone https://github.com/dineshkumarkarimajji/genai-summarizer.git
cd genai-summarizer
