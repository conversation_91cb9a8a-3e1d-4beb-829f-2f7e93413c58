{"documents": [{"id": "3052b2a97743a3f99ee7084e5534f619", "file_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgjf8o_jw.pdf", "chunk_count": 1, "metadata": {"filename": "PromoMate_PRD.pdf", "upload_time": "2025-07-13 03:31:29"}}], "chunks": [{"text": "Product Requirements Document (PRD) PromoMate - WhatsApp Automation Platform 1. Overview PromoMate is an intelligent, user-friendly WhatsApp automation platform designed to help small business owners, shopkeepers, and startups automate their customer outreach, marketing campaigns, and engagement with minimal manual effort. Powered by AI, it allows businesses to send bulk promotional messages, schedule campaigns, auto-generate message content, and collect customer leads all via WhatsApp. 2. Problem Statement Small businesses often lack the time, resources, and technical expertise to manage consistent customer communication and promotions. Manually sending WhatsApp messages, following up on leads, and managing customer queries is inefficient and time-consuming. 3. Solution PromoMate provides an AI-driven platform to automate WhatsApp-based outreach. With features like bulk messaging, campaign scheduling, multilingual AI-generated messages, lead collection, fallback chatbot support, and real-time analytics, the platform helps businesses scale communication and improve engagement without additional manpower. 4. Key Features - Bulk Message Sender - Campaign Scheduler - AI Message Generator (Gemini Flash 2.0) - Simple Chatbot with fallback to owner contact - Lead Collection - Multi-language Support - Advanced Analytics Dashboard - Admin Panel 5. Tech Stack Product Requirements Document (PRD) - Frontend: React.js - Backend: Python (Flask) - Database: MongoDB - AI: Gemini Flash 2.0 API - Hosting: Online - WhatsApp Integration: Not yet finalized 6. Functional Requirements - Admin login & campaign creation - AI-based message generation - Bulk sending and scheduler - Chatbot fallback logic - Lead storage and message logs - Advanced analytics dashboard 7. Non-Functional Requirements - High performance with bulk sending - Secure data storage - Scalable architecture - AI response under 2 seconds - Mobile responsive dashboard 8. User Flow 1. Owner logs into PromoMate dashboard 2. Creates campaign using AI-generated content 3. Schedules or sends immediately 4. Chatbot handles replies or forwards to owner 5. Analytics dashboard shows performance 9. Development Milestones - Week 1: Setup & API Integration - Week 2: AI Message Generator - Week 3: Scheduler Product Requirements Document (PRD) - Week 4: WhatsApp Integration - Week 5: Chatbot + Lead Collection - Week 6: Analytics Dashboard - Week 7: Testing & Deployment - Week 8: Final Report & Demo 10. Limitations - No product database or availability check - Complex queries not handled by chatbot - WhatsApp API usage may incur cost 11. Submission Notes - Online demo with screenshots - GitHub repo with README - Optional video walkthrough", "doc_id": "3052b2a97743a3f99ee7084e5534f619", "chunk_idx": 0, "metadata": {"filename": "PromoMate_PRD.pdf", "upload_time": "2025-07-13 03:31:29", "chunk_num": 1}}], "doc_chunk_map": {"3052b2a97743a3f99ee7084e5534f619": [0]}}