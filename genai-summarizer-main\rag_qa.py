from typing import Dict, List, Optional, Any
from transformers import pipeline
from document_store import DocumentStore
import os
import re
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

class RAGQA:
    def __init__(self, document_store: DocumentStore, model_name: str = "deepset/roberta-base-squad2"):
        """
        Initialize the enhanced RAG-based QA system.

        Args:
            document_store: Instance of DocumentStore for vector search
            model_name: Name of the QA model to use
        """
        self.document_store = document_store
        self.qa_pipeline = pipeline(
            "question-answering",
            model=model_name,
            device=-1  # Use CPU by default
        )

        # Initialize re-ranking model for better context selection
        try:
            # Use the same high-performance embedding model for consistency
            self.rerank_model = SentenceTransformer('nomic-ai/nomic-embed-text-v1')
            print("✅ Using nomic-embed-text-v1 for re-ranking")
        except Exception:
            try:
                self.rerank_model = SentenceTransformer('mixedbread-ai/mxbai-embed-large-v1')
                print("✅ Using mxbai-embed-large-v1 for re-ranking")
            except Exception:
                self.rerank_model = SentenceTransformer('all-mpnet-base-v2')
                print("✅ Using all-mpnet-base-v2 for re-ranking (fallback)")

        # Query expansion patterns
        self.expansion_patterns = {
            'what': ['definition', 'meaning', 'explanation'],
            'how': ['method', 'process', 'procedure', 'way'],
            'why': ['reason', 'cause', 'purpose', 'rationale'],
            'when': ['time', 'date', 'period', 'timing'],
            'where': ['location', 'place', 'position'],
            'who': ['person', 'people', 'individual', 'author']
        }
    
    def _format_context(self, chunks: List[Dict]) -> str:
        """Format context from retrieved chunks"""
        context = ""
        for i, result in enumerate(chunks, 1):
            chunk = result['chunk']
            context += f"[Document {chunk['metadata'].get('chunk_num', i)}]\n{chunk['text']}\n\n"
        return context.strip()
    
    def answer_question(self,
                       question: str,
                       doc_id: Optional[str] = None,
                       top_k: int = 8,
                       min_score: float = 0.5) -> Dict[str, Any]:
        """
        Enhanced answer generation using improved RAG with query expansion and re-ranking.

        Args:
            question: The question to answer
            doc_id: Optional document ID to search within
            top_k: Number of chunks to retrieve
            min_score: Minimum relevance score for considering chunks

        Returns:
            Dictionary containing the answer and metadata with source attribution
        """
        try:
            # Expand query for better retrieval
            expanded_question = self._expand_query(question)

            # Classify question type for better handling
            question_type = self._classify_question(question)

            # Retrieve relevant chunks with enhanced search
            results = self.document_store.search(
                expanded_question,
                k=top_k * 3,  # Get more candidates for better coverage
                doc_id=doc_id,
                hybrid_weight=0.5  # More balanced search for better recall
            )

            # Also try original question if expanded doesn't work well
            if not results or len(results) < 2:
                results = self.document_store.search(
                    question,  # Use original question
                    k=top_k * 2,
                    doc_id=doc_id,
                    hybrid_weight=0.3  # More sparse search for exact matches
                )

            if not results:
                return {
                    'answer': "No relevant information found in the documents.",
                    'context': "",
                    'sources': [],
                    'confidence': 0.0,
                    'citations': []
                }

            # Re-rank results for better relevance
            reranked_results = self._rerank_results(question, results, top_k)

            # Filter by minimum score
            score_threshold = 1 - min_score  # Convert to distance threshold
            filtered_results = [r for r in reranked_results if r.get('distance', 1.0) < score_threshold]

            if not filtered_results and reranked_results:
                filtered_results = reranked_results[:2]  # Use top 2 if none pass threshold

            # Fallback: Direct text search if no good results
            if not filtered_results:
                filtered_results = self._direct_text_search(question, doc_id)

            # Format context with enhanced source attribution
            context = self._format_enhanced_context(filtered_results)

            try:
                # Truncate context if too long to prevent tensor size mismatch
                max_context_length = 400  # Safe length for RoBERTa model
                if len(context) > max_context_length:
                    # Keep the most relevant part (usually at the beginning)
                    context = context[:max_context_length] + "..."

                # Get answer from QA model with safe parameters
                qa_result = self.qa_pipeline(
                    question=question,
                    context=context,
                    max_answer_len=200,  # Safe answer length
                    max_question_len=100,  # Safe question length
                    max_seq_len=512,  # Standard RoBERTa max length
                    handle_impossible_answer=False,  # Force answer from context
                    top_k=1,  # Single best answer
                    doc_stride=64  # Smaller stride for safety
                )
                
                # If model is not confident, try to find relevant sections
                if qa_result['score'] < 0.1 and 'impossible' in str(qa_result).lower():
                    # Look for section headers that might contain the answer
                    section_matches = [r for r in filtered_results 
                                     if any(header in r['chunk']['text'].lower() 
                                          for header in ['result', 'experiment', 'evaluation'])]
                    if section_matches:
                        section_context = self._format_context(section_matches)
                        return {
                            'answer': f"The paper discusses this in the results/evaluation section. Here's the relevant content:\n\n{section_context}",
                            'confidence': 0.7,
                            'context': section_context,
                            'sources': [{
                                'text': r['chunk']['text'],
                                'document_id': r['chunk']['doc_id'],
                                'chunk_num': r['chunk']['metadata'].get('chunk_num', 0),
                                'score': 1 - r['distance']
                            } for r in section_matches]
                        }
                
                # Extract source chunks with more metadata
                sources = []
                seen_chunks = set()
                for r in filtered_results:
                    chunk_text = r['chunk']['text']
                    if chunk_text in seen_chunks:
                        continue
                    seen_chunks.add(chunk_text)
                    
                    # Try to extract section header
                    section = ""
                    lines = chunk_text.split('\n')
                    if len(lines) > 0 and (lines[0].isupper() or lines[0].endswith(':')):
                        section = lines[0]
                    
                    sources.append({
                        'text': chunk_text,
                        'document_id': r['chunk']['doc_id'],
                        'chunk_num': r['chunk']['metadata'].get('chunk_num', 0),
                        'score': 1 - r['distance'],
                        'section': section
                    })
                
                # Sort sources by relevance
                sources.sort(key=lambda x: x['score'], reverse=True)
                
                return {
                    'answer': qa_result['answer'],
                    'confidence': float(qa_result['score']),
                    'context': context,
                    'sources': sources[:5]  # Return top 5 sources max
                }
                
            except Exception as e:
                print(f"Error in QA pipeline: {str(e)}")

                # Handle tensor size errors specifically
                if "tensor" in str(e).lower() or "size" in str(e).lower():
                    try:
                        # Try with much shorter context
                        short_context = context[:300]
                        qa_result = self.qa_pipeline(
                            question=question,
                            context=short_context,
                            max_answer_len=100,
                            max_seq_len=256
                        )
                        return {
                            'answer': qa_result['answer'],
                            'confidence': float(qa_result['score']),
                            'context': short_context,
                            'sources': [{'text': r['chunk']['text'][:300] + '...', 'score': 1 - r['distance']}
                                      for r in filtered_results[:2]]
                        }
                    except Exception:
                        # Final fallback - extract directly from context
                        answer = self._extract_direct_answer(question, context)
                        return {
                            'answer': answer,
                            'confidence': 0.6,
                            'context': context[:500],
                            'sources': [{'text': r['chunk']['text'][:300] + '...', 'score': 1 - r['distance']}
                                      for r in filtered_results[:2]]
                        }

                # For other errors, use the original fallback
                return {
                    'answer': f"I found some relevant information but had trouble processing it. Here are the most relevant sections:\n\n{context[:1000]}...",
                    'confidence': 0.5,
                    'context': context,
                    'sources': [{'text': r['chunk']['text'][:500] + '...', 'score': 1 - r['distance']}
                              for r in filtered_results[:3]]
                }
                
        except Exception as e:
            print(f"Error in answer_question: {str(e)}")
            return {
                'answer': "I encountered an error while processing your question. Please try rephrasing or asking about a different aspect of the paper.",
                'context': "",
                'sources': [],
                'confidence': 0.0
            }

    def _expand_query(self, question: str) -> str:
        """Expand query with related terms for better retrieval."""
        expanded_terms = [question]
        question_lower = question.lower()

        # Add expansions based on question type
        for key_word, expansions in self.expansion_patterns.items():
            if key_word in question_lower:
                expanded_terms.extend(expansions)

        # Add domain-specific expansions
        if any(term in question_lower for term in ['result', 'performance', 'score']):
            expanded_terms.extend(['outcome', 'finding', 'achievement', 'metric'])

        return ' '.join(expanded_terms)

    def _classify_question(self, question: str) -> str:
        """Classify question type for better handling."""
        question_lower = question.lower()

        if any(word in question_lower for word in ['what', 'define', 'definition']):
            return 'factual'
        elif any(word in question_lower for word in ['how', 'process', 'method']):
            return 'procedural'
        elif any(word in question_lower for word in ['why', 'reason', 'cause']):
            return 'causal'
        elif any(word in question_lower for word in ['compare', 'contrast', 'difference']):
            return 'comparative'
        else:
            return 'general'

    def _rerank_results(self, question: str, results: List[Dict], top_k: int) -> List[Dict]:
        """Re-rank search results using cross-encoder for better relevance."""
        if not results or len(results) <= top_k:
            return results

        try:
            # Prepare pairs for re-ranking
            pairs = [(question, result['chunk']['text']) for result in results]

            # Get re-ranking scores
            scores = self.rerank_model.encode(pairs)

            # Combine with original scores
            for i, result in enumerate(results):
                original_score = 1 - result.get('distance', 1.0)
                rerank_score = float(scores[i]) if hasattr(scores[i], 'item') else float(scores[i])
                # Weighted combination
                result['combined_score'] = 0.7 * rerank_score + 0.3 * original_score
                result['rerank_score'] = rerank_score

            # Sort by combined score
            results.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
            return results[:top_k]

        except Exception as e:
            print(f"Error in re-ranking: {e}")
            return results[:top_k]

    def _format_enhanced_context(self, results: List[Dict]) -> str:
        """Format context with enhanced source attribution and better structure."""
        if not results:
            return ""

        context_parts = []
        for result in results:
            chunk = result['chunk']
            chunk_text = chunk['text']

            # Extract and highlight tech stack section if present
            if 'tech stack' in chunk_text.lower():
                # Find the tech stack section and extract it properly
                lines = chunk_text.split('\n')
                tech_section = []
                in_tech_section = False

                for line in lines:
                    line_lower = line.lower().strip()
                    if 'tech stack' in line_lower:
                        in_tech_section = True
                        tech_section.append(line)
                    elif in_tech_section:
                        if line.strip() and (line.startswith('-') or line.startswith('•') or ':' in line):
                            tech_section.append(line)
                        elif line.strip() and not line.startswith(' ') and '.' in line and line[0].isdigit():
                            # Next numbered section, stop
                            break
                        elif line.strip():
                            tech_section.append(line)

                if tech_section:
                    # Use the extracted tech section as primary context
                    formatted_tech = '\n'.join(tech_section)
                    context_parts.append(f"Tech Stack Information:\n{formatted_tech}")
                else:
                    context_parts.append(chunk_text)
            else:
                context_parts.append(chunk_text)

        return "\n\n".join(context_parts)

    def _is_technical_question(self, question: str) -> bool:
        """Determine if question is technical/academic in nature."""
        technical_terms = [
            'result', 'score', 'performance', 'compare', 'achieve',
            'metric', 'evaluation', 'experiment', 'analysis', 'method',
            'algorithm', 'model', 'accuracy', 'precision', 'recall'
        ]
        question_lower = question.lower()
        return any(term in question_lower for term in technical_terms)

    def _direct_text_search(self, question: str, doc_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Direct text search fallback for when semantic search fails."""
        try:
            # Get all chunks for the document
            if doc_id:
                chunks = self.document_store.get_document_chunks(doc_id)
            else:
                chunks = self.document_store.chunks

            # Extract key terms from question
            question_lower = question.lower()
            key_terms = []

            # Common tech stack terms
            tech_terms = ['tech stack', 'technology', 'frontend', 'backend', 'database',
                         'react', 'python', 'flask', 'mongodb', 'gemini', 'api']

            for term in tech_terms:
                if term in question_lower:
                    key_terms.append(term)

            # Search for chunks containing these terms
            matching_chunks = []
            for chunk in chunks:
                chunk_text = chunk['text'].lower()
                score = 0

                # Score based on term matches
                for term in key_terms:
                    if term in chunk_text:
                        score += 1

                # Boost score for exact section matches
                if 'tech stack' in chunk_text:
                    score += 5
                if 'frontend:' in chunk_text or 'backend:' in chunk_text:
                    score += 3

                if score > 0:
                    matching_chunks.append({
                        'chunk': chunk,
                        'score': score / (len(key_terms) + 1),  # Normalize score
                        'distance': 1 - (score / (len(key_terms) + 5))  # Convert to distance
                    })

            # Sort by score and return top results
            matching_chunks.sort(key=lambda x: x['score'], reverse=True)
            return matching_chunks[:3]

        except Exception as e:
            print(f"Error in direct text search: {e}")
            return []

    def _extract_direct_answer(self, question: str, context: str) -> str:
        """Extract answer directly from context when QA model fails."""
        try:
            question_lower = question.lower()
            context_lower = context.lower()

            # For tech stack questions
            if 'tech stack' in question_lower or 'technology' in question_lower:
                # Look for tech stack section
                if 'tech stack' in context_lower:
                    lines = context.split('\n')
                    tech_lines = []
                    found_tech = False

                    for line in lines:
                        if 'tech stack' in line.lower():
                            found_tech = True
                            continue
                        elif found_tech:
                            line = line.strip()
                            if line and (line.startswith('-') or line.startswith('•') or ':' in line):
                                tech_lines.append(line)
                            elif line and line[0].isdigit() and '.' in line:
                                break  # Next section

                    if tech_lines:
                        return "PromoMate's tech stack includes:\n" + '\n'.join(tech_lines)

            # For other questions, return first relevant sentence
            sentences = context.split('.')
            for sentence in sentences[:3]:
                if any(word in sentence.lower() for word in question_lower.split()[:3]):
                    return sentence.strip() + '.'

            # Fallback: return first part of context
            return context[:200] + "..."

        except Exception:
            return "I found relevant information but couldn't extract a specific answer."

    def get_document_qa(self, doc_id: str) -> 'DocumentQA':
        """
        Get a document-specific QA instance.
        
        Args:
            doc_id: ID of the document to focus on
            
        Returns:
            DocumentQA instance scoped to the specified document
        """
        return DocumentQA(self, doc_id)


class DocumentQA:
    """A QA instance scoped to a specific document"""
    def __init__(self, rag_qa: RAGQA, doc_id: str):
        self.rag_qa = rag_qa
        self.doc_id = doc_id
    
    def answer_question(self, question: str, top_k: int = 3, min_score: float = 0.7) -> Dict[str, Any]:
        """
        Answer a question specifically about the scoped document.
        
        Args:
            question: The question to answer
            top_k: Number of chunks to retrieve
            min_score: Minimum relevance score (0-1) for considering chunks
            
        Returns:
            Dictionary containing the answer and metadata
        """
        return self.rag_qa.answer_question(
            question=question,
            doc_id=self.doc_id,
            top_k=top_k,
            min_score=min_score
        )
    
    def get_document_summary(self, max_chunks: int = 5) -> str:
        """
        Generate a summary of the document by analyzing key chunks.
        
        Args:
            max_chunks: Maximum number of chunks to include in summary
            
        Returns:
            A summary of the document
        """
        # Get the most representative chunks (first few chunks often contain good summaries)
        chunks = self.rag_qa.document_store.get_document_chunks(self.doc_id)
        if not chunks:
            return "No content available for this document."
        
        # Take first few chunks as they often contain summaries/introductions
        summary_chunks = chunks[:min(max_chunks, len(chunks))]
        
        # Combine chunks and clean up the text
        summary = " ".join(chunk['text'] for chunk in summary_chunks)
        summary = re.sub(r'\s+', ' ', summary).strip()
        
        # Truncate to a reasonable length
        max_length = 2000
        if len(summary) > max_length:
            summary = summary[:max_length].rsplit(' ', 1)[0] + '...'
            
        return summary
