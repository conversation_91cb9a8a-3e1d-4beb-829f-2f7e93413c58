from typing import Dict, List, Optional, Any
from transformers import pipeline
from document_store import DocumentStore
import os
import re

class RAGQA:
    def __init__(self, document_store: DocumentStore, model_name: str = "deepset/roberta-base-squad2"):
        """
        Initialize the RAG-based QA system.
        
        Args:
            document_store: Instance of DocumentStore for vector search
            model_name: Name of the QA model to use
        """
        self.document_store = document_store
        self.qa_pipeline = pipeline(
            "question-answering",
            model=model_name,
            device=-1  # Use CPU by default
        )
    
    def _format_context(self, chunks: List[Dict]) -> str:
        """Format context from retrieved chunks"""
        context = ""
        for i, result in enumerate(chunks, 1):
            chunk = result['chunk']
            context += f"[Document {chunk['metadata'].get('chunk_num', i)}]\n{chunk['text']}\n\n"
        return context.strip()
    
    def answer_question(self, 
                       question: str, 
                       doc_id: Optional[str] = None, 
                       top_k: int = 8,
                       min_score: float = 0.5) -> Dict[str, Any]:
        """
        Answer a question using RAG with enhanced handling for academic papers.
        
        Args:
            question: The question to answer
            doc_id: Optional document ID to search within
            top_k: Number of chunks to retrieve (increased for better context)
            min_score: Minimum relevance score (0-1) for considering chunks (slightly lowered)
            
        Returns:
            Dictionary containing the answer and metadata
        """
        try:
            # Pre-process question for academic papers
            question_lower = question.lower()
            is_technical = any(term in question_lower for term in 
                             ['result', 'score', 'performance', 'compare', 'achieve',
                              'metric', 'evaluation', 'experiment'])
            
            # Search for relevant chunks with increased top_k for better context
            results = self.document_store.search(question, k=top_k * 2 if is_technical else top_k, doc_id=doc_id)
            
            if not results:
                return {
                    'answer': "No relevant information found in the documents.",
                    'context': "",
                    'sources': [],
                    'confidence': 0.0
                }
            
            # Filter by score but be more lenient for technical questions
            score_threshold = (1 - min_score) * (0.9 if is_technical else 1.0)
            filtered_results = [r for r in results if r['distance'] < score_threshold]
            
            # If no results pass threshold but we have some results, use the top one
            if not filtered_results and results:
                filtered_results = results[:1]
            
            # Format context with section headers if available
            context = self._format_context(filtered_results)
            
            # For technical questions, include more context
            if is_technical and len(context.split()) < 500:  # If context is small
                # Get more chunks but with lower relevance
                additional_results = self.document_store.search(question, k=top_k, doc_id=doc_id)
                additional_context = self._format_context(additional_results)
                context = f"{context}\n\nAdditional context:\n{additional_context}"
            
            try:
                # Get answer from QA model with adjusted parameters for technical content
                qa_result = self.qa_pipeline(
                    question=question,
                    context=context,
                    max_answer_len=300 if is_technical else 200,
                    max_question_len=150,
                    max_seq_len=512,
                    handle_impossible_answer=True
                )
                
                # If model is not confident, try to find relevant sections
                if qa_result['score'] < 0.1 and 'impossible' in str(qa_result).lower():
                    # Look for section headers that might contain the answer
                    section_matches = [r for r in filtered_results 
                                     if any(header in r['chunk']['text'].lower() 
                                          for header in ['result', 'experiment', 'evaluation'])]
                    if section_matches:
                        section_context = self._format_context(section_matches)
                        return {
                            'answer': f"The paper discusses this in the results/evaluation section. Here's the relevant content:\n\n{section_context}",
                            'confidence': 0.7,
                            'context': section_context,
                            'sources': [{
                                'text': r['chunk']['text'],
                                'document_id': r['chunk']['doc_id'],
                                'chunk_num': r['chunk']['metadata'].get('chunk_num', 0),
                                'score': 1 - r['distance']
                            } for r in section_matches]
                        }
                
                # Extract source chunks with more metadata
                sources = []
                seen_chunks = set()
                for r in filtered_results:
                    chunk_text = r['chunk']['text']
                    if chunk_text in seen_chunks:
                        continue
                    seen_chunks.add(chunk_text)
                    
                    # Try to extract section header
                    section = ""
                    lines = chunk_text.split('\n')
                    if len(lines) > 0 and (lines[0].isupper() or lines[0].endswith(':')):
                        section = lines[0]
                    
                    sources.append({
                        'text': chunk_text,
                        'document_id': r['chunk']['doc_id'],
                        'chunk_num': r['chunk']['metadata'].get('chunk_num', 0),
                        'score': 1 - r['distance'],
                        'section': section
                    })
                
                # Sort sources by relevance
                sources.sort(key=lambda x: x['score'], reverse=True)
                
                return {
                    'answer': qa_result['answer'],
                    'confidence': float(qa_result['score']),
                    'context': context,
                    'sources': sources[:5]  # Return top 5 sources max
                }
                
            except Exception as e:
                print(f"Error in QA pipeline: {str(e)}")
                return {
                    'answer': f"I found some relevant information but had trouble processing it. Here are the most relevant sections:\n\n{context[:2000]}...",
                    'confidence': 0.5,
                    'context': context,
                    'sources': [{'text': r['chunk']['text'][:500] + '...', 'score': 1 - r['distance']} 
                              for r in filtered_results[:3]]
                }
                
        except Exception as e:
            print(f"Error in answer_question: {str(e)}")
            return {
                'answer': "I encountered an error while processing your question. Please try rephrasing or asking about a different aspect of the paper.",
                'context': "",
                'sources': [],
                'confidence': 0.0
            }
            
        except Exception as e:
            return {
                'answer': f"Error generating answer: {str(e)}",
                'context': context,
                'sources': [],
                'confidence': 0.0
            }
    
    def get_document_qa(self, doc_id: str) -> 'DocumentQA':
        """
        Get a document-specific QA instance.
        
        Args:
            doc_id: ID of the document to focus on
            
        Returns:
            DocumentQA instance scoped to the specified document
        """
        return DocumentQA(self, doc_id)


class DocumentQA:
    """A QA instance scoped to a specific document"""
    def __init__(self, rag_qa: RAGQA, doc_id: str):
        self.rag_qa = rag_qa
        self.doc_id = doc_id
    
    def answer_question(self, question: str, top_k: int = 3, min_score: float = 0.7) -> Dict[str, Any]:
        """
        Answer a question specifically about the scoped document.
        
        Args:
            question: The question to answer
            top_k: Number of chunks to retrieve
            min_score: Minimum relevance score (0-1) for considering chunks
            
        Returns:
            Dictionary containing the answer and metadata
        """
        return self.rag_qa.answer_question(
            question=question,
            doc_id=self.doc_id,
            top_k=top_k,
            min_score=min_score
        )
    
    def get_document_summary(self, max_chunks: int = 5) -> str:
        """
        Generate a summary of the document by analyzing key chunks.
        
        Args:
            max_chunks: Maximum number of chunks to include in summary
            
        Returns:
            A summary of the document
        """
        # Get the most representative chunks (first few chunks often contain good summaries)
        chunks = self.rag_qa.document_store.get_document_chunks(self.doc_id)
        if not chunks:
            return "No content available for this document."
        
        # Take first few chunks as they often contain summaries/introductions
        summary_chunks = chunks[:min(max_chunks, len(chunks))]
        
        # Combine chunks and clean up the text
        summary = " ".join(chunk['text'] for chunk in summary_chunks)
        summary = re.sub(r'\s+', ' ', summary).strip()
        
        # Truncate to a reasonable length
        max_length = 2000
        if len(summary) > max_length:
            summary = summary[:max_length].rsplit(' ', 1)[0] + '...'
            
        return summary
