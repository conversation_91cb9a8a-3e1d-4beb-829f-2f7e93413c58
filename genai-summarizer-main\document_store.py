from typing import List, Dict, Any, Optional
import os
import fitz  # PyMuPDF
import docx2txt
import pandas as pd
from sentence_transformers import SentenceTransformer
import numpy as np
import faiss
import hashlib
import json
from pathlib import Path
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class DocumentStore:
    def __init__(self, persist_dir: str = "./vector_store"):
        self.persist_dir = Path(persist_dir)
        self.persist_dir.mkdir(exist_ok=True)

        # Initialize the embedding model - upgraded to a more powerful model
        try:
            # Try to use a more powerful model first
            self.embedding_model = SentenceTransformer('all-mpnet-base-v2')
            self.embedding_dim = 768  # Dimension for all-mpnet-base-v2
        except Exception:
            # Fallback to the original model if the new one fails
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            self.embedding_dim = 384  # Dimension for all-MiniLM-L6-v2

        # Initialize FAISS index with better indexing
        self.index = faiss.IndexFlatL2(self.embedding_dim)
        self.documents = []  # Stores document metadata
        self.chunks = []     # Stores text chunks
        self.doc_chunk_map = {}  # Maps document_id to chunk indices

        # Initialize TF-IDF vectorizer for hybrid search
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english',
            ngram_range=(1, 2),
            max_df=0.95,
            min_df=2
        )
        self.tfidf_matrix = None

        self.load_existing()
    
    def load_existing(self):
        """Load existing vector store if available"""
        index_path = self.persist_dir / "index.faiss"
        meta_path = self.persist_dir / "metadata.json"
        
        if index_path.exists() and meta_path.exists():
            self.index = faiss.read_index(str(index_path))
            with open(meta_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.documents = data['documents']
                self.chunks = data['chunks']
                self.doc_chunk_map = data['doc_chunk_map']
    
    def save(self):
        """Save the current state to disk"""
        # Save FAISS index
        faiss.write_index(self.index, str(self.persist_dir / "index.faiss"))
        
        # Save metadata
        with open(self.persist_dir / "metadata.json", 'w', encoding='utf-8') as f:
            json.dump({
                'documents': self.documents,
                'chunks': self.chunks,
                'doc_chunk_map': self.doc_chunk_map
            }, f, ensure_ascii=False, indent=2)
    
    def _get_file_hash(self, file_path: str) -> str:
        """Generate a unique hash for a file"""
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            buf = f.read()
            hasher.update(buf)
        return hasher.hexdigest()
    
    def _process_document(self, file_path: str) -> str:
        """Extract text from different document types"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == '.pdf':
                doc = fitz.open(file_path)
                text = ""
                for page in doc:
                    text += page.get_text()
                return text
            
            elif file_ext == '.docx':
                return docx2txt.process(file_path)
            
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
                return df.to_string()
            
            elif file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            else:
                raise ValueError(f"Unsupported file type: {file_ext}")
                
        except Exception as e:
            raise Exception(f"Error processing {file_path}: {str(e)}")
    
    def _chunk_text(self, text: str, chunk_size: int = 800, overlap: int = 150) -> List[str]:
        """
        Split text into meaningful chunks with improved semantic awareness.

        Args:
            text: Input text to chunk
            chunk_size: Target number of words per chunk (reduced for better context)
            overlap: Number of words to overlap between chunks (increased for better continuity)

        Returns:
            List of text chunks
        """
        # Clean and normalize text
        text = re.sub(r'\s+', ' ', text.strip())

        # Split by sentences first for better semantic boundaries
        sentences = re.split(r'(?<=[.!?])\s+', text)

        chunks = []
        current_chunk = []
        current_length = 0

        for sentence in sentences:
            sentence_words = sentence.split()
            sentence_length = len(sentence_words)

            # If single sentence is too long, split it carefully
            if sentence_length > chunk_size:
                # Save current chunk if it has content
                if current_chunk:
                    chunks.append(' '.join(current_chunk))
                    current_chunk = []
                    current_length = 0

                # Split long sentence at natural break points (commas, semicolons)
                sub_parts = re.split(r'[,;]\s+', sentence)
                temp_chunk = []
                temp_length = 0

                for part in sub_parts:
                    part_words = part.split()
                    if temp_length + len(part_words) > chunk_size and temp_chunk:
                        chunks.append(' '.join(temp_chunk))
                        temp_chunk = part_words[-overlap:] if len(part_words) > overlap else part_words
                        temp_length = len(temp_chunk)
                    else:
                        temp_chunk.extend(part_words)
                        temp_length += len(part_words)

                if temp_chunk:
                    chunks.append(' '.join(temp_chunk))
            else:
                # Check if adding this sentence would exceed chunk size
                if current_length + sentence_length > chunk_size and current_chunk:
                    chunks.append(' '.join(current_chunk))
                    # Create overlap from the end of previous chunk
                    overlap_words = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                    current_chunk = overlap_words.copy()
                    current_length = len(overlap_words)

                # Add sentence to current chunk
                current_chunk.extend(sentence_words)
                current_length += sentence_length
        
        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        # Clean up any empty chunks
        return [chunk for chunk in chunks if chunk.strip()]
    
    def add_document(self, file_path: str, metadata: Optional[Dict] = None) -> str:
        """Add a document to the vector store"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Generate document ID
        doc_id = self._get_file_hash(file_path)
        
        # Check if document already exists
        if any(doc['id'] == doc_id for doc in self.documents):
            return doc_id
        
        # Process document
        text = self._process_document(file_path)
        chunks = self._chunk_text(text)
        
        # Generate embeddings for chunks
        chunk_embeddings = self.embedding_model.encode(chunks, show_progress_bar=False)
        
        # Update FAISS index
        if len(self.chunks) == 0:
            self.index = faiss.IndexFlatL2(chunk_embeddings.shape[1])
        
        # Add vectors to index
        self.index.add(chunk_embeddings)
        
        # Update metadata
        doc_metadata = {
            'id': doc_id,
            'file_path': file_path,
            'chunk_count': len(chunks),
            'metadata': metadata or {}
        }
        self.documents.append(doc_metadata)
        
        # Update chunk tracking
        start_idx = len(self.chunks)
        # Create chunks with metadata
        chunk_metadata = metadata.copy() if metadata else {}
        for i, chunk in enumerate(chunks):
            chunk_metadata['chunk_num'] = i + 1
            self.chunks.append({
                'text': chunk,
                'doc_id': doc_id,
                'chunk_idx': i,
                'metadata': chunk_metadata.copy()  # Create a copy for each chunk
            })
        
        self.doc_chunk_map[doc_id] = list(range(start_idx, len(self.chunks)))

        # Update TF-IDF matrix for hybrid search
        self._update_tfidf_matrix()

        # Save the updated index and metadata
        self.save()

        return doc_id

    def _update_tfidf_matrix(self):
        """Update the TF-IDF matrix with all current chunks."""
        try:
            if self.chunks:
                chunk_texts = [chunk['text'] for chunk in self.chunks]
                self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(chunk_texts)
        except Exception as e:
            print(f"Error updating TF-IDF matrix: {e}")
            self.tfidf_matrix = None
    
    def search(self, query: str, k: int = 3, doc_id: Optional[str] = None, hybrid_weight: float = 0.7) -> List[Dict[str, Any]]:
        """
        Enhanced hybrid search combining dense embeddings and sparse TF-IDF.

        Args:
            query: Search query
            k: Number of results to return
            doc_id: Optional document ID to search within
            hybrid_weight: Weight for dense search (0.0 = only sparse, 1.0 = only dense)
        """
        try:
            if not query or not isinstance(query, str) or not query.strip() or k <= 0:
                return []

            # Expand query with synonyms and related terms
            expanded_query = self._expand_query(query)

            # Get candidate chunks
            if doc_id:
                candidate_chunks = self._get_document_chunks_for_search(doc_id)
            else:
                candidate_chunks = [(i, chunk) for i, chunk in enumerate(self.chunks)]

            if not candidate_chunks:
                return []

            # Dense search using embeddings
            dense_scores = self._dense_search(expanded_query, candidate_chunks, k * 2)

            # Sparse search using TF-IDF
            sparse_scores = self._sparse_search(expanded_query, candidate_chunks, k * 2)

            # Combine scores using hybrid weighting
            combined_scores = self._combine_scores(dense_scores, sparse_scores, hybrid_weight)

            # Re-rank and return top k results
            return self._rerank_results(combined_scores, k)

        except Exception as e:
            print(f"Error in hybrid search: {str(e)}")
            return []

    def _expand_query(self, query: str) -> str:
        """Expand query with related terms for better retrieval."""
        # Simple query expansion - can be enhanced with word embeddings
        expanded_terms = []
        words = query.lower().split()

        # Add original query
        expanded_terms.append(query)

        # Add variations for common academic terms
        expansions = {
            'result': ['results', 'outcome', 'finding', 'conclusion'],
            'method': ['methodology', 'approach', 'technique', 'procedure'],
            'analysis': ['analyze', 'examination', 'evaluation', 'assessment'],
            'performance': ['efficiency', 'effectiveness', 'accuracy', 'score'],
            'compare': ['comparison', 'versus', 'contrast', 'difference'],
            'improve': ['enhancement', 'optimization', 'better', 'increase']
        }

        for word in words:
            if word in expansions:
                expanded_terms.extend(expansions[word])

        return ' '.join(expanded_terms)

    def _get_document_chunks_for_search(self, doc_id: str) -> List[tuple]:
        """Get chunks for a specific document."""
        if doc_id not in self.doc_chunk_map:
            return []

        chunk_indices = self.doc_chunk_map[doc_id]
        return [(i, self.chunks[i]) for i in chunk_indices if i < len(self.chunks)]

    def _dense_search(self, query: str, candidate_chunks: List[tuple], k: int) -> Dict[int, float]:
        """Perform dense search using embeddings."""
        if not candidate_chunks:
            return {}

        try:
            # Encode query
            query_embedding = self.embedding_model.encode([query])[0]

            # Get embeddings for candidate chunks
            chunk_texts = [chunk['text'] for _, chunk in candidate_chunks]
            chunk_embeddings = self.embedding_model.encode(chunk_texts)

            # Calculate cosine similarities
            similarities = cosine_similarity([query_embedding], chunk_embeddings)[0]

            # Create score dictionary
            scores = {}
            for i, (chunk_idx, _) in enumerate(candidate_chunks):
                scores[chunk_idx] = float(similarities[i])

            return scores
        except Exception as e:
            print(f"Error in dense search: {e}")
            return {}

    def _sparse_search(self, query: str, candidate_chunks: List[tuple], k: int) -> Dict[int, float]:
        """Perform sparse search using TF-IDF."""
        if not candidate_chunks or self.tfidf_matrix is None:
            return {}

        try:
            # Get texts for candidate chunks
            chunk_texts = [chunk['text'] for _, chunk in candidate_chunks]

            # Fit TF-IDF if not already fitted or update with new texts
            if not hasattr(self.tfidf_vectorizer, 'vocabulary_') or len(chunk_texts) != self.tfidf_matrix.shape[0]:
                self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(chunk_texts)

            # Transform query
            query_vector = self.tfidf_vectorizer.transform([query])

            # Calculate similarities
            similarities = cosine_similarity(query_vector, self.tfidf_matrix)[0]

            # Create score dictionary
            scores = {}
            for i, (chunk_idx, _) in enumerate(candidate_chunks):
                scores[chunk_idx] = float(similarities[i])

            return scores
        except Exception as e:
            print(f"Error in sparse search: {e}")
            return {}

    def _combine_scores(self, dense_scores: Dict[int, float], sparse_scores: Dict[int, float],
                       hybrid_weight: float) -> Dict[int, float]:
        """Combine dense and sparse scores."""
        combined = {}
        all_indices = set(dense_scores.keys()) | set(sparse_scores.keys())

        for idx in all_indices:
            dense_score = dense_scores.get(idx, 0.0)
            sparse_score = sparse_scores.get(idx, 0.0)
            combined[idx] = hybrid_weight * dense_score + (1 - hybrid_weight) * sparse_score

        return combined

    def _rerank_results(self, scores: Dict[int, float], k: int) -> List[Dict[str, Any]]:
        """Re-rank and format final results."""
        # Sort by score (descending)
        sorted_indices = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        results = []
        for idx in sorted_indices[:k]:
            if idx < len(self.chunks):
                results.append({
                    'chunk': self.chunks[idx],
                    'score': scores[idx],
                    'distance': 1.0 - scores[idx]  # Convert similarity to distance
                })

        return results

    def get_document_chunks(self, doc_id: str) -> List[Dict]:
        """Get all chunks for a specific document"""
        if not hasattr(self, 'doc_chunk_map') or not hasattr(self, 'chunks'):
            return []
            
        if doc_id not in self.doc_chunk_map:
            return []
            
        chunk_indices = self.doc_chunk_map[doc_id]
        return [self.chunks[i] for i in chunk_indices if 0 <= i < len(self.chunks)]
    
    def delete_document(self, doc_id: str) -> bool:
        """Remove a document and its chunks from the store"""
        try:
            # Check if document exists
            if not hasattr(self, 'documents') or not hasattr(self, 'chunks') or not hasattr(self, 'doc_chunk_map'):
                return False
                
            if doc_id not in self.doc_chunk_map:
                return False
            
            # Remove document metadata
            self.documents = [doc for doc in self.documents if doc.get('id') != doc_id]
            
            # Get chunk indices to remove
            chunk_indices = set(self.doc_chunk_map[doc_id])
            
            # Remove chunks
            new_chunks = [chunk for i, chunk in enumerate(self.chunks) 
                         if i not in chunk_indices]
            
            # Rebuild FAISS index if needed
            if new_chunks:
                try:
                    chunk_texts = [chunk['text'] for chunk in new_chunks]
                    chunk_embeddings = self.embedding_model.encode(chunk_texts, show_progress_bar=False)
                    
                    self.index = faiss.IndexFlatL2(chunk_embeddings.shape[1])
                    self.index.add(chunk_embeddings)
                    self.chunks = new_chunks
                except Exception as e:
                    print(f"Error rebuilding index: {str(e)}")
                    return False
            else:
                self.index = faiss.IndexFlatL2(self.embedding_dim)
                self.chunks = []
            
            # Update doc_chunk_map
            del self.doc_chunk_map[doc_id]
            
            # Update indices in other documents
            for doc in list(self.doc_chunk_map.keys()):
                old_indices = self.doc_chunk_map[doc]
                new_indices = []
                
                for idx in old_indices:
                    removed_before = sum(1 for i in chunk_indices if i < idx)
                    new_idx = idx - removed_before
                    if 0 <= new_idx < len(self.chunks):
                        new_indices.append(new_idx)
                
                if new_indices:
                    self.doc_chunk_map[doc] = new_indices
                else:
                    del self.doc_chunk_map[doc]
            
            # Save changes
            self.save()
            return True
            
        except Exception as e:
            print(f"Error deleting document: {str(e)}")
            return False
