from typing import List, Dict, Any, Optional
import os
import fitz  # PyMuPDF
import docx2txt
import pandas as pd
from sentence_transformers import SentenceTransformer
import numpy as np
import faiss
import hashlib
import json
from pathlib import Path

class DocumentStore:
    def __init__(self, persist_dir: str = "./vector_store"):
        self.persist_dir = Path(persist_dir)
        self.persist_dir.mkdir(exist_ok=True)
        
        # Initialize the embedding model
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.embedding_dim = 384  # Dimension for all-MiniLM-L6-v2
        
        # Initialize FAISS index
        self.index = faiss.IndexFlatL2(self.embedding_dim)
        self.documents = []  # Stores document metadata
        self.chunks = []     # Stores text chunks
        self.doc_chunk_map = {}  # Maps document_id to chunk indices
        
        self.load_existing()
    
    def load_existing(self):
        """Load existing vector store if available"""
        index_path = self.persist_dir / "index.faiss"
        meta_path = self.persist_dir / "metadata.json"
        
        if index_path.exists() and meta_path.exists():
            self.index = faiss.read_index(str(index_path))
            with open(meta_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.documents = data['documents']
                self.chunks = data['chunks']
                self.doc_chunk_map = data['doc_chunk_map']
    
    def save(self):
        """Save the current state to disk"""
        # Save FAISS index
        faiss.write_index(self.index, str(self.persist_dir / "index.faiss"))
        
        # Save metadata
        with open(self.persist_dir / "metadata.json", 'w', encoding='utf-8') as f:
            json.dump({
                'documents': self.documents,
                'chunks': self.chunks,
                'doc_chunk_map': self.doc_chunk_map
            }, f, ensure_ascii=False, indent=2)
    
    def _get_file_hash(self, file_path: str) -> str:
        """Generate a unique hash for a file"""
        hasher = hashlib.md5()
        with open(file_path, 'rb') as f:
            buf = f.read()
            hasher.update(buf)
        return hasher.hexdigest()
    
    def _process_document(self, file_path: str) -> str:
        """Extract text from different document types"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == '.pdf':
                doc = fitz.open(file_path)
                text = ""
                for page in doc:
                    text += page.get_text()
                return text
            
            elif file_ext == '.docx':
                return docx2txt.process(file_path)
            
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
                return df.to_string()
            
            elif file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            else:
                raise ValueError(f"Unsupported file type: {file_ext}")
                
        except Exception as e:
            raise Exception(f"Error processing {file_path}: {str(e)}")
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 100) -> List[str]:
        """
        Split text into meaningful chunks, preserving section structure.
        
        Args:
            text: Input text to chunk
            chunk_size: Target number of words per chunk
            overlap: Number of words to overlap between chunks
            
        Returns:
            List of text chunks
        """
        # First, split by sections (assuming sections are separated by double newlines)
        sections = [s.strip() for s in text.split('\n\n') if s.strip()]
        
        chunks = []
        current_chunk = []
        current_length = 0
        
        for section in sections:
            # If section is a header (short and ends with a colon or is in all caps)
            is_header = (len(section.split()) < 10 and 
                        (section.endswith(':') or section.isupper()))
            
            section_words = section.split()
            section_length = len(section_words)
            
            # If section is too long, split it into smaller chunks
            if section_length > chunk_size:
                # If we have content in current chunk, save it first
                if current_chunk:
                    chunks.append(' '.join(current_chunk))
                    current_chunk = []
                    current_length = 0
                
                # Split the long section into chunks
                for i in range(0, section_length, chunk_size - overlap):
                    chunk = ' '.join(section_words[i:i + chunk_size])
                    chunks.append(chunk)
            else:
                # If adding this section would exceed chunk size, save current chunk
                if current_length + section_length > chunk_size and current_chunk:
                    chunks.append(' '.join(current_chunk))
                    # Keep some overlap
                    overlap_words = current_chunk[-overlap:] if current_chunk else []
                    current_chunk = overlap_words.copy()
                    current_length = len(overlap_words)
                
                # Add section to current chunk
                if is_header and current_chunk:
                    # Ensure headers are at the start of a chunk
                    chunks.append(' '.join(current_chunk))
                    current_chunk = section_words
                    current_length = section_length
                else:
                    if current_chunk:
                        current_chunk.append('\n\n')
                    current_chunk.extend(section_words)
                    current_length += section_length
        
        # Add the last chunk if it's not empty
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        # Clean up any empty chunks
        return [chunk for chunk in chunks if chunk.strip()]
    
    def add_document(self, file_path: str, metadata: Optional[Dict] = None) -> str:
        """Add a document to the vector store"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Generate document ID
        doc_id = self._get_file_hash(file_path)
        
        # Check if document already exists
        if any(doc['id'] == doc_id for doc in self.documents):
            return doc_id
        
        # Process document
        text = self._process_document(file_path)
        chunks = self._chunk_text(text)
        
        # Generate embeddings for chunks
        chunk_embeddings = self.embedding_model.encode(chunks, show_progress_bar=False)
        
        # Update FAISS index
        if len(self.chunks) == 0:
            self.index = faiss.IndexFlatL2(chunk_embeddings.shape[1])
        
        # Add vectors to index
        self.index.add(chunk_embeddings)
        
        # Update metadata
        doc_metadata = {
            'id': doc_id,
            'file_path': file_path,
            'chunk_count': len(chunks),
            'metadata': metadata or {}
        }
        self.documents.append(doc_metadata)
        
        # Update chunk tracking
        start_idx = len(self.chunks)
        # Create chunks with metadata
        chunk_metadata = metadata.copy() if metadata else {}
        for i, chunk in enumerate(chunks):
            chunk_metadata['chunk_num'] = i + 1
            self.chunks.append({
                'text': chunk,
                'doc_id': doc_id,
                'chunk_idx': i,
                'metadata': chunk_metadata.copy()  # Create a copy for each chunk
            })
        
        self.doc_chunk_map[doc_id] = list(range(start_idx, len(self.chunks)))
        
        # Save the updated index and metadata
        self.save()
        
        return doc_id
    
    def search(self, query: str, k: int = 3, doc_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search for relevant chunks"""
        try:
            if not query or not isinstance(query, str) or not query.strip() or k <= 0:
                return []
                
            # Encode query
            query_embedding = self.embedding_model.encode([query])
            
            # Search in FAISS
            if doc_id:
                # Search only within specific document
                if not hasattr(self, 'doc_chunk_map') or doc_id not in self.doc_chunk_map:
                    return []
                    
                chunk_indices = self.doc_chunk_map.get(doc_id, [])
                if not chunk_indices or not hasattr(self, 'chunks') or not self.chunks:
                    return []
                
                try:
                    # Get embeddings for document chunks
                    doc_embeddings = []
                    valid_indices = []
                    
                    for i in chunk_indices:
                        if 0 <= i < len(self.chunks):
                            try:
                                embedding = self.embedding_model.encode([self.chunks[i]['text']])[0]
                                doc_embeddings.append(embedding)
                                valid_indices.append(i)
                            except Exception as e:
                                print(f"Error encoding chunk {i}: {str(e)}")
                    
                    if not doc_embeddings:
                        return []
                        
                    doc_embeddings = np.array(doc_embeddings)
                    
                    # Calculate distances
                    distances = np.linalg.norm(doc_embeddings - query_embedding, axis=1)
                    
                    # Get top k results
                    k = min(max(1, k), len(distances))
                    if k <= 0:
                        return []
                        
                    # Get indices of top k smallest distances
                    top_k_indices = np.argpartition(distances, min(k, len(distances)-1))[:k]
                    
                    results = []
                    for idx in top_k_indices:
                        if 0 <= idx < len(valid_indices):
                            chunk_idx = valid_indices[idx]
                            if 0 <= chunk_idx < len(self.chunks):
                                results.append({
                                    'chunk': self.chunks[chunk_idx],
                                    'distance': float(distances[idx])
                                })
                    
                    # Sort by distance
                    results.sort(key=lambda x: x['distance'])
                    return results
                    
                except Exception as e:
                    print(f"Error in document search: {str(e)}")
                    return []
            
            else:
                # Search across all documents
                if not hasattr(self, 'index') or not hasattr(self, 'chunks') or not self.chunks:
                    return []
                    
                try:
                    if self.index.ntotal == 0:
                        return []
                        
                    # Ensure k is valid
                    k = min(max(1, k), self.index.ntotal)
                    if k <= 0:
                        return []
                        
                    # Perform search
                    distances, indices = self.index.search(query_embedding, k)
                    
                    results = []
                    for i, idx in enumerate(indices[0]):
                        if 0 <= idx < len(self.chunks):
                            results.append({
                                'chunk': self.chunks[idx],
                                'distance': float(distances[0][i])
                            })
                    
                    # Sort by distance
                    results.sort(key=lambda x: x['distance'])
                    return results
                    
                except Exception as e:
                    print(f"Error in global search: {str(e)}")
                    return []
                    
        except Exception as e:
            print(f"Unexpected error in search: {str(e)}")
            return []
    
    def get_document_chunks(self, doc_id: str) -> List[Dict]:
        """Get all chunks for a specific document"""
        if not hasattr(self, 'doc_chunk_map') or not hasattr(self, 'chunks'):
            return []
            
        if doc_id not in self.doc_chunk_map:
            return []
            
        chunk_indices = self.doc_chunk_map[doc_id]
        return [self.chunks[i] for i in chunk_indices if 0 <= i < len(self.chunks)]
    
    def delete_document(self, doc_id: str) -> bool:
        """Remove a document and its chunks from the store"""
        try:
            # Check if document exists
            if not hasattr(self, 'documents') or not hasattr(self, 'chunks') or not hasattr(self, 'doc_chunk_map'):
                return False
                
            if doc_id not in self.doc_chunk_map:
                return False
            
            # Remove document metadata
            self.documents = [doc for doc in self.documents if doc.get('id') != doc_id]
            
            # Get chunk indices to remove
            chunk_indices = set(self.doc_chunk_map[doc_id])
            
            # Remove chunks
            new_chunks = [chunk for i, chunk in enumerate(self.chunks) 
                         if i not in chunk_indices]
            
            # Rebuild FAISS index if needed
            if new_chunks:
                try:
                    chunk_texts = [chunk['text'] for chunk in new_chunks]
                    chunk_embeddings = self.embedding_model.encode(chunk_texts, show_progress_bar=False)
                    
                    self.index = faiss.IndexFlatL2(chunk_embeddings.shape[1])
                    self.index.add(chunk_embeddings)
                    self.chunks = new_chunks
                except Exception as e:
                    print(f"Error rebuilding index: {str(e)}")
                    return False
            else:
                self.index = faiss.IndexFlatL2(self.embedding_dim)
                self.chunks = []
            
            # Update doc_chunk_map
            del self.doc_chunk_map[doc_id]
            
            # Update indices in other documents
            for doc in list(self.doc_chunk_map.keys()):
                old_indices = self.doc_chunk_map[doc]
                new_indices = []
                
                for idx in old_indices:
                    removed_before = sum(1 for i in chunk_indices if i < idx)
                    new_idx = idx - removed_before
                    if 0 <= new_idx < len(self.chunks):
                        new_indices.append(new_idx)
                
                if new_indices:
                    self.doc_chunk_map[doc] = new_indices
                else:
                    del self.doc_chunk_map[doc]
            
            # Save changes
            self.save()
            return True
            
        except Exception as e:
            print(f"Error deleting document: {str(e)}")
            return False
